{"name": "inventory-tracker-gen-frontend", "version": "0.1.0", "private": true, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.0.18", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.4.0", "chart.js": "^4.3.0", "framer-motion": "^10.12.16", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^3.1.2", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.44.3", "react-icons": "^5.5.0", "react-query": "^3.39.3", "react-router-dom": "^6.12.1", "react-scripts": "5.0.1", "react-toastify": "^9.1.3", "recharts": "^2.6.2", "web-vitals": "^2.1.4", "zustand": "^4.3.8"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "format": "prettier --write \"src/**/*.{js,jsx,json,css,scss,md}\"", "prepare": "husky"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.14", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "husky": "^9.1.7", "lint-staged": "^15.5.1", "postcss": "^8.4.24", "prettier": "^3.5.3", "tailwindcss": "^3.3.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,jsx,json,css,scss,md}": ["prettier --write"]}}