import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { multiPageOcrService } from '../../services/api';
import { useToast } from '../../context/ToastContext';
import { useNavigate } from 'react-router-dom';

// Sortable Item Component
const SortableItem = ({ file, index, onRemove }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: file.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`flex items-center p-3 border rounded-lg mb-2 ${
        isDragging ? 'bg-blue-50 border-blue-300' : 'bg-gray-50 border-gray-200'
      } cursor-move`}
    >
      <div className="flex-shrink-0 mr-3">
        <span className="inline-flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 text-blue-800 text-sm font-medium">
          {file.pageNumber}
        </span>
      </div>

      {file.preview && (
        <div className="flex-shrink-0 mr-3">
          <img
            src={file.preview}
            alt={`Preview of ${file.name}`}
            className="h-12 w-12 object-cover rounded"
          />
        </div>
      )}

      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 truncate">{file.name}</p>
        <p className="text-sm text-gray-500">
          {(file.size / 1024 / 1024).toFixed(1)} MB • {file.type}
        </p>
      </div>

      <button
        onClick={(e) => {
          e.stopPropagation();
          onRemove(file.id);
        }}
        className="flex-shrink-0 ml-3 text-red-600 hover:text-red-800"
      >
        <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>
  );
};

const MultiFileUploader = () => {
  const [files, setFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedVendor, setSelectedVendor] = useState('generic');
  const [error, setError] = useState('');
  const [uploadResult, setUploadResult] = useState(null);

  const toast = useToast();
  const navigate = useNavigate();

  // DnD Kit sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  // File validation
  const validateFile = (file) => {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    const maxSize = 50 * 1024 * 1024; // 50MB

    if (!validTypes.includes(file.type)) {
      return `Invalid file type: ${file.type}. Please upload JPG, PNG, or PDF files.`;
    }

    if (file.size > maxSize) {
      return `File too large: ${(file.size / 1024 / 1024).toFixed(1)}MB. Maximum size is 50MB.`;
    }

    return null;
  };

  // Handle file drop
  const onDrop = useCallback(
    (acceptedFiles, rejectedFiles) => {
      setError('');

      // Handle rejected files
      if (rejectedFiles.length > 0) {
        const errors = rejectedFiles.map(
          ({ file, errors }) => `${file.name}: ${errors.map((e) => e.message).join(', ')}`,
        );
        setError(`Some files were rejected: ${errors.join('; ')}`);
      }

      // Validate and add accepted files
      const newFiles = [];
      const validationErrors = [];

      acceptedFiles.forEach((file, index) => {
        const validationError = validateFile(file);
        if (validationError) {
          validationErrors.push(`${file.name}: ${validationError}`);
        } else {
          const fileWithPreview = Object.assign(file, {
            preview: URL.createObjectURL(file),
            id: `${Date.now()}-${index}`,
            pageNumber: files.length + newFiles.length + 1,
          });
          newFiles.push(fileWithPreview);
        }
      });

      if (validationErrors.length > 0) {
        setError(`Validation errors: ${validationErrors.join('; ')}`);
      }

      // Check total file count
      const totalFiles = files.length + newFiles.length;
      if (totalFiles > 20) {
        setError('Maximum 20 files allowed per multi-page invoice');
        return;
      }

      if (totalFiles < 2 && newFiles.length > 0) {
        toast.info('Add at least 2 files for multi-page invoice processing');
      }

      setFiles((prev) => [...prev, ...newFiles]);
    },
    [files.length, toast],
  );

  // Configure dropzone
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'application/pdf': ['.pdf'],
    },
    maxFiles: 20,
    multiple: true,
  });

  // Remove file
  const removeFile = (fileId) => {
    setFiles((prev) => {
      const updated = prev.filter((f) => f.id !== fileId);
      // Renumber pages
      return updated.map((file, index) => ({
        ...file,
        pageNumber: index + 1,
      }));
    });
  };

  // Handle drag end for reordering
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setFiles((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over.id);

        const reorderedFiles = arrayMove(items, oldIndex, newIndex);

        // Renumber pages
        return reorderedFiles.map((file, index) => ({
          ...file,
          pageNumber: index + 1,
        }));
      });
    }
  };

  // Upload files
  const handleUpload = async () => {
    if (files.length < 2) {
      setError('Please select at least 2 files for multi-page invoice processing');
      toast.warning('Please select at least 2 files for multi-page invoice processing');
      return;
    }

    setUploading(true);
    setError('');
    setUploadProgress(0);

    try {
      // Create page order array
      const pageOrder = files.map((_, index) => index);

      // Convert files to File objects (remove preview URLs)
      const fileObjects = files.map((f) => {
        const { preview, id, pageNumber, ...fileData } = f;
        return new File([f], f.name, { type: f.type });
      });

      toast.info('Uploading multi-page invoice... This may take a few minutes.', {
        autoClose: false,
      });

      const response = await multiPageOcrService.uploadMultiPageInvoice(
        fileObjects,
        selectedVendor,
        pageOrder,
      );

      setUploadResult(response.data);
      toast.success(
        `Multi-page invoice uploaded successfully! ${response.data.page_count} pages processed.`,
      );

      // Navigate to the invoice detail page after a short delay
      setTimeout(() => {
        navigate(`/invoices/${response.data.invoice_id}`);
      }, 2000);
    } catch (err) {
      console.error('Upload error:', err);
      const errorMessage = err.response?.data?.error_details || err.message || 'Upload failed';
      setError(errorMessage);
      toast.error(`Upload failed: ${errorMessage}`);
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  // Clear all files
  const clearFiles = () => {
    files.forEach((file) => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview);
      }
    });
    setFiles([]);
    setError('');
    setUploadResult(null);
  };

  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Multi-Page Invoice Upload</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {uploadResult && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
          <span className="block sm:inline">
            ✅ Upload successful! Invoice ID: {uploadResult.invoice_id}({uploadResult.page_count}{' '}
            pages processed)
          </span>
        </div>
      )}

      {/* File Drop Zone */}
      <div className="bg-white shadow rounded-lg p-6 mb-6">
        <h2 className="text-lg font-semibold mb-4">Select Files</h2>

        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
            isDragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:bg-gray-50'
          }`}
        >
          <input {...getInputProps()} />
          <div className="flex flex-col items-center">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              stroke="currentColor"
              fill="none"
              viewBox="0 0 48 48"
            >
              <path
                d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <p className="mt-2 text-sm text-gray-600">
              {isDragActive
                ? 'Drop the files here...'
                : 'Drag & drop files here, or click to select files'}
            </p>
            <p className="text-xs text-gray-500 mt-1">
              JPG, PNG, or PDF files up to 50MB each (2-20 files)
            </p>
          </div>
        </div>

        {/* Vendor Selection */}
        <div className="mt-4">
          <label htmlFor="vendor-select" className="block text-sm font-medium text-gray-700 mb-1">
            Select Vendor
          </label>
          <select
            id="vendor-select"
            value={selectedVendor}
            onChange={(e) => setSelectedVendor(e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="generic">Generic (Default)</option>
            <option value="bova">Bova</option>
            <option value="kast">Kast</option>
            <option value="dekalb">Dekalb Produce</option>
          </select>
          <p className="mt-1 text-sm text-gray-500">
            Selecting the correct vendor improves OCR accuracy for multi-page invoices
          </p>
        </div>
      </div>

      {/* File List */}
      {files.length > 0 && (
        <div className="bg-white shadow rounded-lg p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Selected Files ({files.length}/20)</h2>
            <button onClick={clearFiles} className="text-sm text-red-600 hover:text-red-800">
              Clear All
            </button>
          </div>

          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext items={files.map((f) => f.id)} strategy={verticalListSortingStrategy}>
              {files.map((file, index) => (
                <SortableItem
                  key={file.id}
                  file={file}
                  index={index}
                  onRemove={removeFile}
                />
              ))}
            </SortableContext>
          </DndContext>

          {files.length >= 2 && (
            <div className="mt-4 flex justify-end">
              <button
                onClick={handleUpload}
                disabled={uploading}
                className={`px-6 py-2 rounded-md text-white font-medium ${
                  uploading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                }`}
              >
                {uploading ? 'Uploading...' : `Upload ${files.length} Files`}
              </button>
            </div>
          )}
        </div>
      )}

      {files.length > 0 && files.length < 2 && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative">
          <span className="block sm:inline">
            Add at least one more file to create a multi-page invoice (minimum 2 files required)
          </span>
        </div>
      )}
    </div>
  );
};

export default MultiFileUploader;
