import React from 'react';
import PropTypes from 'prop-types';
import { createContext, useContext, useState, useEffect } from 'react';
import jwtDecode from 'jwt-decode';
import { authService } from '../services/api';

const AuthContext = createContext();

export const useAuth = () => {
  return useContext(AuthContext);
};

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    // Check if user is already logged in
    const token = localStorage.getItem('token');
    if (token) {
      try {
        // Verify token expiration
        const decoded = jwtDecode(token);
        const currentTime = Date.now() / 1000;

        if (decoded.exp < currentTime) {
          // Token expired
          logout();
        } else {
          // Get user data from token
          setCurrentUser({
            id: decoded.sub,
            username: decoded.username || '',
            email: decoded.email || '',
            restaurant_name: decoded.restaurant_name || '',
          });
          setIsAuthenticated(true);

          // Optionally fetch the latest user data from the server
          fetchCurrentUser();
        }
      } catch (err) {
        console.error('Invalid token:', err);
        logout();
      }
    }
    setLoading(false);
  }, []);

  const fetchCurrentUser = async () => {
    try {
      const response = await authService.getCurrentUser();
      setCurrentUser(response.data.user);
    } catch (err) {
      console.error('Error fetching current user:', err);
      // If we can't fetch the current user, the token might be invalid
      if (err.response && err.response.status === 401) {
        logout();
      }
    }
  };

  const register = async (username, email, password, restaurant_name) => {
    setError('');
    try {
      const response = await authService.register({
        username,
        email,
        password,
        restaurant_name,
      });

      // If registration automatically logs in the user
      if (response.data && response.data.access_token) {
        localStorage.setItem('token', response.data.access_token);
        setCurrentUser(
          response.data.user || {
            username,
            email,
            restaurant_name,
          },
        );
        setIsAuthenticated(true);
      }

      return { success: true, data: response.data };
    } catch (err) {
      console.error('Registration error:', err);
      const errorMessage = err.response?.data?.message || 'Registration failed. Please try again.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const login = async (email, password) => {
    console.log('AuthContext: login attempt started for email:', email);
    console.log('AuthContext: API URL being used:', process.env.REACT_APP_API_URL);
    setError('');
    try {
      console.log('AuthContext: calling authService.login...');
      const response = await authService.login({
        email,
        password,
      });

      console.log(
        'AuthContext: received login response:',
        response ? { status: response.status, hasData: !!response.data } : 'No response',
      );

      // Check if we have user data at minimum
      if (!response.data || !response.data.user) {
        console.error(
          'AuthContext: Invalid response - missing user data',
          response.data ? 'Has data but no user' : 'No data',
        );
        throw new Error('Invalid response from server');
      }

      const { user } = response.data;
      console.log('AuthContext: login successful, user data:', user ? 'present' : 'missing');

      // If access_token is available in response, store it as backup
      if (response.data.access_token) {
        console.log('AuthContext: token received in response body');
        localStorage.setItem('token', response.data.access_token);
        console.log('AuthContext: token stored in localStorage');
      } else {
        console.log('AuthContext: No token in response body, will rely on HTTP-only cookie');
      }

      // Update state
      setCurrentUser(user || { email });
      setIsAuthenticated(true);
      console.log('AuthContext: user state updated, authentication successful');

      return { success: true };
    } catch (err) {
      // Enhanced error logging for debugging
      const errorDetails = {
        message: err.message,
        name: err.name,
        stack: err.stack,
        response: err.response
          ? {
              status: err.response.status,
              data: err.response.data,
              headers: err.response.headers,
            }
          : 'No response data',
        request: err.request
          ? {
              responseURL: err.request.responseURL,
              status: err.request.status,
              responseType: err.request.responseType,
              withCredentials: err.request.withCredentials,
            }
          : 'No request made',
        config: err.config
          ? {
              url: err.config.url,
              method: err.config.method,
              headers: err.config.headers,
              baseURL: err.config.baseURL,
            }
          : 'No config available',
      };

      console.error('AuthContext: Login error details:', errorDetails);

      // Extract status code and error message for more specific error handling
      const statusCode = err.response?.status;
      const errorMessage =
        err.response?.data?.message || 'Login failed. Please check your credentials.';

      setError(errorMessage);
      return {
        success: false,
        error: errorMessage,
        statusCode: statusCode,
      };
    }
  };

  const logout = () => {
    // Remove token from localStorage
    localStorage.removeItem('token');

    // Update state
    setCurrentUser(null);
    setIsAuthenticated(false);
  };

  const updateProfile = async (userData) => {
    setError('');
    try {
      const response = await authService.updateProfile(userData);
      setCurrentUser(response.data.user);
      return { success: true, data: response.data };
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Profile update failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const value = {
    currentUser,
    isAuthenticated,
    loading,
    error,
    register,
    login,
    logout,
    updateProfile,
  };

  return <AuthContext.Provider value={value}>{!loading && children}</AuthContext.Provider>;
};

AuthProvider.propTypes = {
  children: PropTypes.node.isRequired,
};
